// Simple notification system for auto-refresh events
class NotificationManager {
  constructor() {
    this.notifications = [];
    this.listeners = [];
  }

  // Add a notification
  add(notification) {
    const id = Date.now() + Math.random();
    const newNotification = {
      id,
      timestamp: new Date(),
      ...notification
    };
    
    this.notifications.unshift(newNotification);
    
    // Keep only last 50 notifications
    if (this.notifications.length > 50) {
      this.notifications = this.notifications.slice(0, 50);
    }
    
    // Notify listeners
    this.listeners.forEach(listener => listener(this.notifications));
    
    // Auto-remove after 5 seconds for success notifications
    if (notification.type === 'success') {
      setTimeout(() => {
        this.remove(id);
      }, 5000);
    }
    
    return id;
  }

  // Remove a notification
  remove(id) {
    this.notifications = this.notifications.filter(n => n.id !== id);
    this.listeners.forEach(listener => listener(this.notifications));
  }

  // Subscribe to notification changes
  subscribe(listener) {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  // Get all notifications
  getAll() {
    return this.notifications;
  }

  // Clear all notifications
  clear() {
    this.notifications = [];
    this.listeners.forEach(listener => listener(this.notifications));
  }
}

// Create singleton instance
const notificationManager = new NotificationManager();

// Helper functions for common notification types
export const showTokenRefreshSuccess = (accountEmail) => {
  return notificationManager.add({
    type: 'success',
    title: 'Token Refreshed',
    message: `Token cho ${accountEmail} đã được làm mới thành công`,
    icon: '✅'
  });
};

export const showTokenRefreshError = (accountEmail, error) => {
  return notificationManager.add({
    type: 'error',
    title: 'Token Refresh Failed',
    message: `Không thể làm mới token cho ${accountEmail}: ${error.message}`,
    icon: '❌'
  });
};

export const showTokenExpiryWarning = (accountEmail, hoursLeft) => {
  return notificationManager.add({
    type: 'warning',
    title: 'Token Expiring Soon',
    message: `Token cho ${accountEmail} sẽ hết hạn trong ${hoursLeft} giờ`,
    icon: '⚠️'
  });
};

export const showAutoRefreshStarted = (accountEmail) => {
  return notificationManager.add({
    type: 'info',
    title: 'Auto Refresh Started',
    message: `Đang tự động làm mới token cho ${accountEmail}...`,
    icon: '🔄'
  });
};

// Export the manager for direct access
export default notificationManager;
