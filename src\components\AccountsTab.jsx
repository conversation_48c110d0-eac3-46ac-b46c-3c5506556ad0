import React, { useState, useEffect } from 'react';
import { Mail, Plus, ExternalLink, RefreshCw, Trash2, CheckCircle, AlertCircle, Clock, Copy, Share2, Users, Eye, Download } from 'lucide-react';
import { apiCall } from '../utils/api';

const AccountsTab = ({ loading, setLoading, accounts, loadAccounts }) => {
  const [authLink, setAuthLink] = useState('');
  const [showLinkModal, setShowLinkModal] = useState(false);
  const [sellerInfo, setSellerInfo] = useState({ name: '', location: '', note: '' });
  const [showAccountDetails, setShowAccountDetails] = useState(null);
  const [accountStats, setAccountStats] = useState({});

  // Load account statistics on mount and when accounts change
  useEffect(() => {
    if (accounts.length > 0) {
      loadAccountStats();
    }
  }, [accounts]);

  // Load account statistics
  const loadAccountStats = async () => {
    try {
      const statsPromises = accounts.map(async (account) => {
        try {
          const stats = await apiCall(`/Accounts/${account.id}/stats`);
          return { [account.id]: stats };
        } catch (error) {
          console.error(`Failed to load stats for account ${account.id}:`, error);
          return { [account.id]: null };
        }
      });
      
      const statsArray = await Promise.all(statsPromises);
      const statsObject = statsArray.reduce((acc, curr) => ({ ...acc, ...curr }), {});
      setAccountStats(statsObject);
    } catch (error) {
      console.error('Error loading account stats:', error);
    }
  };

  // Generate shareable auth link
  const generateAuthLink = async () => {
    if (!sellerInfo.name.trim()) {
      alert('Vui lòng nhập tên seller');
      return;
    }

    try {
      setLoading(true);
      
      const data = await apiCall('/Auth/auth-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sellerInfo
        })
      });
      
      if (data.success) {
        setAuthLink(data.authUrl);
        setShowLinkModal(true);
      } else {
        throw new Error(data.message || 'Không thể tạo link xác thực');
      }
      
    } catch (error) {
      console.error('Generate auth link error:', error);
      alert('Lỗi khi tạo link xác thực: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Copy link to clipboard
  const copyLinkToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(authLink);
      alert('📋 Đã copy link vào clipboard!');
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = authLink;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('📋 Đã copy link vào clipboard!');
    }
  };

  // Direct login - opens in popup window
  const directLogin = () => {
    if (!sellerInfo.name.trim()) {
      alert('Vui lòng nhập tên seller');
      return;
    }
    
    // Open auth URL in new popup window
    const popup = window.open(
      authLink, 
      'oauth-login', 
      'width=500,height=600,scrollbars=yes,resizable=yes,centerscreen=yes'
    );
    
    // Check when popup is closed to refresh accounts
    if (popup) {
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          // Refresh accounts list after popup closes
          setTimeout(() => {
            loadAccounts();
            resetForm();
          }, 1000);
        }
      }, 1000);
    }
  };

  // Delete account
  const deleteAccount = async (accountId) => {
    if (!confirm('Bạn có chắc chắn muốn xóa tài khoản này?')) {
      return;
    }
    
    try {
      setLoading(true);
      const response = await apiCall(`/Accounts/${accountId}`, { 
        method: 'DELETE' 
      });
      
      if (response.success !== false) {
        await loadAccounts();
        alert('Tài khoản đã được xóa thành công!');
      } else {
        throw new Error(response.message || 'Không thể xóa tài khoản');
      }
    } catch (error) {
      console.error('Delete account error:', error);
      alert('Lỗi khi xóa tài khoản: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Refresh token
  const refreshToken = async (accountId) => {
    try {
      setLoading(true);
      const response = await apiCall(`/Auth/refresh-token/${accountId}`, {
        method: 'POST'
      });

      if (response.message || response.success !== false) {
        await loadAccounts();
        await loadAccountStats();
        alert('Token đã được làm mới thành công!');
      } else {
        throw new Error(response.error || 'Không thể làm mới token');
      }
    } catch (error) {
      console.error('Refresh token error:', error);
      alert('Lỗi khi làm mới token: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Toggle account active status
  const toggleAccountStatus = async (accountId, currentStatus) => {
    try {
      setLoading(true);
      const response = await apiCall(`/Accounts/${accountId}/toggle-status`, {
        method: 'POST'
      });
      
      if (response.success !== false) {
        await loadAccounts();
        alert(`Tài khoản đã được ${currentStatus ? 'tắt' : 'bật'} thành công!`);
      } else {
        throw new Error(response.message || 'Không thể thay đổi trạng thái tài khoản');
      }
    } catch (error) {
      console.error('Toggle status error:', error);
      alert('Lỗi khi thay đổi trạng thái: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Update seller info
  const updateSellerInfo = async (accountId, newSellerInfo) => {
    try {
      setLoading(true);
      const response = await apiCall(`/Accounts/${accountId}/seller-info`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sellerInfo: newSellerInfo })
      });
      
      if (response.success !== false) {
        await loadAccounts();
        alert('Thông tin seller đã được cập nhật!');
        setShowAccountDetails(null);
      } else {
        throw new Error(response.message || 'Không thể cập nhật thông tin seller');
      }
    } catch (error) {
      console.error('Update seller info error:', error);
      alert('Lỗi khi cập nhật thông tin: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Export account data
  const exportAccountData = async (accountId) => {
    try {
      setLoading(true);
      const response = await apiCall(`/Accounts/${accountId}/export`);
      
      // Create and download file
      const blob = new Blob([JSON.stringify(response, null, 2)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `account-${accountId}-export.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      alert('Dữ liệu đã được xuất thành công!');
    } catch (error) {
      console.error('Export error:', error);
      alert('Lỗi khi xuất dữ liệu: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Get account status with enhanced logic
  const getAccountStatus = (account) => {
    if (!account.isActive) {
      return { icon: AlertCircle, color: 'bg-red-100 text-red-600', text: 'Đã tắt' };
    } else if (account.tokenExpired) {
      return { icon: AlertCircle, color: 'bg-red-100 text-red-600', text: 'Token hết hạn' };
    } else if (account.status === 'pending') {
      return { icon: Clock, color: 'bg-yellow-100 text-yellow-600', text: 'Đang chờ xác thực' };
    } else if (account.isActive) {
      return { icon: CheckCircle, color: 'bg-green-100 text-green-600', text: 'Đã kết nối' };
    }
    return { icon: AlertCircle, color: 'bg-gray-100 text-gray-600', text: 'Không xác định' };
  };

  // Reset form
  const resetForm = () => {
    setSellerInfo({ name: '', location: '', note: '' });
    setAuthLink('');
    setShowLinkModal(false);
  };

  return (
    <div className="space-y-6">
      <div className="w-full bg-white rounded-xl shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Quản lý tài khoản</h2>
        
        {/* Seller Registration Form */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 mb-6 border border-blue-200">
          <h3 className="text-lg font-semibold mb-4 text-blue-900 flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Thêm Seller mới
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-blue-800 mb-2">Tên Seller *</label>
              <input
                type="text"
                placeholder="VD: Nguyễn Văn A"
                value={sellerInfo.name}
                onChange={(e) => setSellerInfo({...sellerInfo, name: e.target.value})}
                className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-blue-800 mb-2">Vị trí/Khu vực</label>
              <input
                type="text"
                placeholder="VD: Hà Nội, TP.HCM, Đà Nẵng..."
                value={sellerInfo.location}
                onChange={(e) => setSellerInfo({...sellerInfo, location: e.target.value})}
                className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-blue-800 mb-2">Ghi chú</label>
            <textarea
              placeholder="Thông tin thêm về seller này..."
              value={sellerInfo.note}
              onChange={(e) => setSellerInfo({...sellerInfo, note: e.target.value})}
              rows={2}
              className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={generateAuthLink}
              disabled={loading || !sellerInfo.name.trim()}
              className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 shadow-md disabled:opacity-50"
            >
              <Share2 className="h-5 w-5" />
              <span>Tạo Link đăng nhập</span>
            </button>
            
            {authLink && (
              <button
                onClick={directLogin}
                disabled={loading || !sellerInfo.name.trim()}
                className="flex-1 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2 shadow-md disabled:opacity-50"
              >
                <ExternalLink className="h-5 w-5" />
                <span>Đăng nhập ngay</span>
              </button>
            )}
            
            <button
              onClick={resetForm}
              className="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Reset
            </button>
          </div>
        </div>

        {/* Share Link Modal */}
        {showLinkModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl p-6 max-w-2xl w-full">
              <h3 className="text-xl font-bold text-gray-900 mb-4">🔗 Link đăng nhập cho Seller</h3>
              
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
                <p className="text-sm text-gray-700 mb-2"><strong>Seller:</strong> {sellerInfo.name}</p>
                {sellerInfo.location && <p className="text-sm text-gray-700 mb-2"><strong>Vị trí:</strong> {sellerInfo.location}</p>}
                {sellerInfo.note && <p className="text-sm text-gray-700"><strong>Ghi chú:</strong> {sellerInfo.note}</p>}
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">Link đăng nhập:</label>
                <div className="flex">
                  <input
                    type="text"
                    value={authLink}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg bg-gray-50 text-sm"
                  />
                  <button
                    onClick={copyLinkToClipboard}
                    className="bg-blue-600 text-white px-4 py-2 rounded-r-lg hover:bg-blue-700 transition-colors"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </div>
              </div>
              
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                <h4 className="font-semibold text-yellow-800 mb-2">📋 Hướng dẫn gửi cho Seller:</h4>
                <ol className="text-sm text-yellow-700 space-y-1">
                  <li>1. Copy link phía trên và gửi cho seller</li>
                  <li>2. Seller click vào link để mở trang đăng nhập Google</li>
                  <li>3. Seller đăng nhập Gmail và cấp quyền cho ứng dụng</li>
                  <li>4. Tài khoản sẽ tự động xuất hiện trong danh sách</li>
                </ol>
              </div>
              
              <div className="flex space-x-4">
                <button
                  onClick={copyLinkToClipboard}
                  className="flex-1 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
                >
                  <Copy className="h-4 w-4" />
                  <span>Copy Link</span>
                </button>
                <button
                  onClick={() => setShowLinkModal(false)}
                  className="flex-1 bg-gray-200 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  Đóng
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Account Details Modal */}
        {showAccountDetails && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <h3 className="text-xl font-bold text-gray-900 mb-4">📊 Chi tiết tài khoản</h3>
              
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold mb-2">Thông tin tài khoản</h4>
                  <p><strong>Email:</strong> {showAccountDetails.email}</p>
                  <p><strong>Tên hiển thị:</strong> {showAccountDetails.sellerInfo?.name || showAccountDetails.displayName}</p>
                  <p><strong>Trạng thái:</strong> {getAccountStatus(showAccountDetails).text}</p>
                  <p><strong>Tạo lúc:</strong> {new Date(showAccountDetails.createdAt).toLocaleString('vi-VN')}</p>
                  {showAccountDetails.lastSyncAt && (
                    <p><strong>Đồng bộ cuối:</strong> {new Date(showAccountDetails.lastSyncAt).toLocaleString('vi-VN')}</p>
                  )}
                </div>

                {showAccountDetails.sellerInfo && (
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="font-semibold mb-2">Thông tin Seller</h4>
                    <p><strong>Tên:</strong> {showAccountDetails.sellerInfo.name}</p>
                    {showAccountDetails.sellerInfo.location && (
                      <p><strong>Vị trí:</strong> {showAccountDetails.sellerInfo.location}</p>
                    )}
                    {showAccountDetails.sellerInfo.note && (
                      <p><strong>Ghi chú:</strong> {showAccountDetails.sellerInfo.note}</p>
                    )}
                  </div>
                )}

                {accountStats[showAccountDetails.id] && (
                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="font-semibold mb-2">Thống kê</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <p><strong>Email đã xử lý:</strong> {accountStats[showAccountDetails.id].totalEmailsProcessed || 0}</p>
                      <p><strong>Lần chạy:</strong> {accountStats[showAccountDetails.id].processingRuns || 0}</p>
                      <p><strong>Tình trạng:</strong> {accountStats[showAccountDetails.id].isHealthy ? '✅ Khỏe mạnh' : '⚠️ Có vấn đề'}</p>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="flex space-x-4 mt-6">
                <button
                  onClick={() => exportAccountData(showAccountDetails.id)}
                  className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center space-x-2"
                >
                  <Download className="h-4 w-4" />
                  <span>Xuất dữ liệu</span>
                </button>
                <button
                  onClick={() => setShowAccountDetails(null)}
                  className="flex-1 bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  Đóng
                </button>
              </div>
            </div>
          </div>
        )}


        {/* Accounts List */}
        {/* Accounts List */}
        <div className="grid gap-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-900">📧 Danh sách tài khoản đã kết nối ({accounts.length})</h3>
            <button
              onClick={loadAccounts}
              disabled={loading}
              className="bg-blue-100 text-blue-700 px-4 py-2 rounded-lg hover:bg-blue-200 transition-colors disabled:opacity-50 flex items-center space-x-2"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
          
          {accounts.map((account) => {
            const { icon: StatusIcon, color, text } = getAccountStatus(account);
            const stats = accountStats[account.id];
            
            return (
              <div key={account.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="bg-blue-100 p-3 rounded-full">
                      <Mail className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {account.sellerInfo?.name || account.displayName || account.email}
                      </h4>
                      <p className="text-gray-600">{account.email}</p>
                      
                      {/* Seller Info */}
                      {account.sellerInfo && (
                        <div className="mt-1 text-xs text-gray-500">
                          {/* <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">
                            👤 {account.sellerInfo.name}
                          </span> */}
                          {account.sellerInfo.location && (
                            <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                              📍 {account.sellerInfo.location}
                            </span>
                          )}
                        </div>
                      )}
                      
                      <div className="flex items-center space-x-2 mt-1">
                        <div className={`p-1 rounded-full ${color}`}>
                          <StatusIcon className="h-3 w-3" />
                        </div>
                        <span className="text-sm text-gray-500">{text}</span>
                        {stats && (
                          <span className="text-xs text-gray-400">
                            • {stats.totalEmailsProcessed || 0} emails
                          </span>
                        )}
                      </div>
                      
                      {account.lastSyncAt && (
                        <p className="text-xs text-gray-400 mt-1">
                          Đồng bộ lần cuối: {new Date(account.lastSyncAt).toLocaleString('vi-VN')}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setShowAccountDetails(account)}
                      className="bg-blue-100 text-blue-700 p-2 rounded-lg hover:bg-blue-200 transition-colors"
                      title="Xem chi tiết"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => refreshToken(account.id)}
                      disabled={loading}
                      className="bg-green-100 text-green-700 p-2 rounded-lg hover:bg-green-200 transition-colors disabled:opacity-50"
                      title="Refresh Token"
                    >
                      <RefreshCw className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => deleteAccount(account.id)}
                      disabled={loading}
                      className="bg-red-100 text-red-700 p-2 rounded-lg hover:bg-red-200 transition-colors disabled:opacity-50"
                      title="Xóa tài khoản"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
          
          {accounts.length === 0 && !loading && (
            <div className="text-center py-8 text-gray-500">
              <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Chưa có seller nào kết nối</p>
              <p className="text-sm">Tạo link đăng nhập để mời seller tham gia</p>
            </div>
          )}
          
          {loading && (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto text-blue-600" />
              <p className="text-gray-500 mt-2">Đang tải...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AccountsTab;