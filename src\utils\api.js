const API_BASE = 'https://localhost:7126/api';

// Store for auto-refresh callbacks
let autoRefreshCallbacks = {
  onAccountsUpdate: null,
  onTokenRefreshSuccess: null,
  onTokenRefreshFailed: null
};

// Set callbacks for auto-refresh functionality
export const setAutoRefreshCallbacks = (callbacks) => {
  autoRefreshCallbacks = { ...autoRefreshCallbacks, ...callbacks };
};

// Auto-refresh token for a specific account
const autoRefreshAccountToken = async (accountId) => {
  try {
    console.log(`🔄 Auto-refreshing token for account ${accountId}`);

    const response = await fetch(`${API_BASE}/Auth/refresh-token/${accountId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('lsb-token')}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Token auto-refreshed successfully for account ${accountId}`);

      // Notify success callback
      if (autoRefreshCallbacks.onTokenRefreshSuccess) {
        autoRefreshCallbacks.onTokenRefreshSuccess(accountId, data);
      }

      // Refresh accounts list
      if (autoRefreshCallbacks.onAccountsUpdate) {
        autoRefreshCallbacks.onAccountsUpdate();
      }

      return true;
    } else {
      throw new Error(`Failed to refresh token: ${response.status}`);
    }
  } catch (error) {
    console.error(`❌ Auto-refresh failed for account ${accountId}:`, error);

    // Notify failure callback
    if (autoRefreshCallbacks.onTokenRefreshFailed) {
      autoRefreshCallbacks.onTokenRefreshFailed(accountId, error);
    }

    return false;
  }
};

// Check if error indicates token expiration and attempt auto-refresh
const handleTokenExpiration = async (error, endpoint, options, accountId = null) => {
  const errorMessage = error.message.toLowerCase();
  const isTokenExpired = errorMessage.includes('token') &&
                        (errorMessage.includes('expired') ||
                         errorMessage.includes('invalid') ||
                         errorMessage.includes('unauthorized'));

  if (isTokenExpired && accountId) {
    console.log(`🔍 Token expiration detected for account ${accountId}, attempting auto-refresh...`);

    const refreshSuccess = await autoRefreshAccountToken(accountId);

    if (refreshSuccess) {
      // Retry the original request
      console.log(`🔄 Retrying original request after token refresh...`);
      return await apiCall(endpoint, options);
    }
  }

  // If not token expiration or refresh failed, throw original error
  throw error;
};

// Extract account ID from endpoint or options
const extractAccountId = (endpoint, options) => {
  // Try to extract from endpoint patterns
  const accountIdMatch = endpoint.match(/\/Accounts\/([^\/]+)/);
  if (accountIdMatch) {
    return accountIdMatch[1];
  }

  // Try to extract from batch-process body
  if (endpoint.includes('batch-process') && options.body) {
    try {
      const body = JSON.parse(options.body);
      if (body.accountIds && body.accountIds.length === 1) {
        return body.accountIds[0];
      }
    } catch (e) {
      // Ignore parsing errors
    }
  }

  // Try to extract from options if provided
  if (options.accountId) {
    return options.accountId;
  }

  return null;
};

export const apiCall = async (endpoint, options = {}) => {
  try {
    // Ensure endpoint starts with /
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

    // Add authorization header if token exists
    const token = localStorage.getItem('lsb-token');
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response = await fetch(`${API_BASE}${cleanEndpoint}`, {
      headers,
      ...options
    });

    // Check if response is ok (200-299)
    if (!response.ok) {
      // Try to get error details from response
      let errorData;
      try {
        errorData = await response.json();
      } catch {
        errorData = { error: `HTTP error! status: ${response.status}` };
      }

      // Create error with details
      const error = new Error(errorData.error || errorData.message || `HTTP error! status: ${response.status}`);

      // Extract account ID for potential auto-refresh
      const accountId = extractAccountId(cleanEndpoint, options);

      // Handle token expiration with auto-refresh
      return await handleTokenExpiration(error, cleanEndpoint, options, accountId);
    }

    // Try to parse JSON response
    let data;
    try {
      data = await response.json();
    } catch {
      // If response is not JSON, return success indicator
      return { success: true };
    }

    // Check if API returned an error in success response
    if (data.success === false) {
      const error = new Error(data.error || data.message || 'API returned error');

      // Extract account ID for potential auto-refresh
      const accountId = extractAccountId(cleanEndpoint, options);

      // Handle token expiration with auto-refresh
      return await handleTokenExpiration(error, cleanEndpoint, options, accountId);
    }

    return data;

  } catch (error) {
    console.error(`API call failed for ${endpoint}:`, error);
    throw error;
  }
};