const API_BASE = 'https://localhost:7126/api';

export const apiCall = async (endpoint, options = {}) => {
  try {
    // Ensure endpoint starts with /
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

    const response = await fetch(`${API_BASE}${cleanEndpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    // Check if response is ok (200-299)
    if (!response.ok) {
      // Try to get error details from response
      let errorData;
      try {
        errorData = await response.json();
      } catch {
        errorData = { error: `HTTP error! status: ${response.status}` };
      }

      // Throw error with details
      throw new Error(errorData.error || errorData.message || `HTTP error! status: ${response.status}`);
    }

    // Try to parse JSON response
    let data;
    try {
      data = await response.json();
    } catch {
      // If response is not JSON, return success indicator
      return { success: true };
    }

    // Check if API returned an error in success response
    if (data.success === false) {
      throw new Error(data.error || data.message || 'API returned error');
    }

    return data;

  } catch (error) {
    console.error(`API call failed for ${endpoint}:`, error);
    throw error;
  }
};