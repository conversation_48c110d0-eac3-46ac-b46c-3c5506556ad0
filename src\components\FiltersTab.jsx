import React, { useState, useEffect } from 'react';
import { 
  Filter, 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  Search, 
  Mail, 
  User, 
  FileText, 
  Tag, 
  Calendar,
  Paperclip,
  Star,
  AlertCircle,
  Check,
  Eye,
  ArrowDownCircle,
  ArrowUpCircle,
  DollarSign,
  Zap,
  Copy,
  Target,
  Settings,
  TestTube,
  Activity
} from 'lucide-react';

// API configuration - Import your API utility
const API_BASE = 'https://localhost:7126/api';

export const apiCall = async (endpoint, options = {}) => {
  try {
    // Ensure endpoint starts with /
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    
    const response = await fetch(`${API_BASE}${cleanEndpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    // Check if response is ok (200-299)
    if (!response.ok) {
      // Try to get error details from response
      let errorData;
      try {
        errorData = await response.json();
      } catch {
        errorData = { error: `HTTP error! status: ${response.status}` };
      }
      
      // Throw error with details
      throw new Error(errorData.error || errorData.message || `HTTP error! status: ${response.status}`);
    }

    // Try to parse JSON response
    let data;
    try {
      data = await response.json();
    } catch {
      // If response is not JSON, return success indicator
      return { success: true };
    }

    // Check if API returned an error in success response
    if (data.success === false) {
      throw new Error(data.error || data.message || 'API returned error');
    }

    return data;
    
  } catch (error) {
    console.error(`API call failed for ${endpoint}:`, error);
    throw error;
  }
};

const IntegratedFilterWithAPI = () => {
  // State management
  const [filters, setFilters] = useState([]);
  const [emailTemplates, setEmailTemplates] = useState([]);
  const [filterStats, setFilterStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [newFilter, setNewFilter] = useState({ 
    name: '', 
    description: '',
    type: 'custom',
    templateId: '',
    isActive: true,
    matchCondition: 'any',
    conditions: [
      {
        id: Date.now().toString(),
        type: 'sender',
        operator: 'contains',
        value: ''
      }
    ],
    gmailFilters: {
      labels: [],
      hasAttachment: '',
      isRead: '',
      isImportant: '',
      isStarred: '',
      dateRange: {
        type: 'any',
        value: '',
        startDate: null,
        endDate: null
      }
    },
    extractionRules: {},
    customQuery: ''
  });

  const [showFilterForm, setShowFilterForm] = useState(false);
  const [editingFilter, setEditingFilter] = useState(null);
  const [filterErrors, setFilterErrors] = useState({});
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [showTestDialog, setShowTestDialog] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [sampleEmail, setSampleEmail] = useState('');

  const conditionTypes = [
    { value: 'sender', label: 'Người gửi', icon: User },
    { value: 'title', label: 'Tiêu đề', icon: FileText },
    { value: 'body', label: 'Nội dung', icon: Mail }
  ];

  const operators = [
    { value: 'contains', label: 'Chứa' },
    { value: 'not_contains', label: 'Không chứa' },
    { value: 'equals', label: 'Bằng chính xác' },
    { value: 'starts_with', label: 'Bắt đầu bằng' },
    { value: 'ends_with', label: 'Kết thúc bằng' }
  ];

  // Load data on component mount
  useEffect(() => {
    loadFilters();
    loadTemplates();
    loadStats();
  }, []);

  // API calls
  const loadFilters = async () => {
    try {
      setLoading(true);
      const data = await apiCall('/Filters');
      setFilters(data || []);
    } catch (error) {
      console.error('Error loading filters:', error);
      alert('Lỗi khi tải danh sách bộ lọc: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const loadTemplates = async () => {
    try {
      const data = await apiCall('/Filters/templates');
      setEmailTemplates(data || []);
    } catch (error) {
      console.error('Error loading templates:', error);
      // Don't show error for templates as it's not critical
    }
  };

  const loadStats = async () => {
    try {
      const data = await apiCall('/Filters/stats');
      setFilterStats(data);
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  // Apply template to filter
  const applyTemplate = async (template) => {
    try {
      // If it's a template from API, get full details
      if (template.id && !template.conditions) {
        const fullTemplate = await apiCall(`/Filters/templates/${template.id}`);
        template = fullTemplate;
      }

      setNewFilter(prev => ({
        ...prev,
        name: template.name,
        type: 'template',
        templateId: template.id,
        conditions: template.conditions?.map((condition, index) => ({
          ...condition,
          id: (Date.now() + index).toString() // Ensure ID is string
        })) || [],
        extractionRules: template.extractionRules || {},
        description: `Auto-generated filter for ${template.platform} ${template.type} emails`
      }));
      setSelectedTemplate(template);
    } catch (error) {
      console.error('Apply template error:', error);
      alert('Lỗi khi áp dụng template: ' + error.message);
    }
  };

  // Create quick filter for income/outcome
  const createQuickFilter = async (type) => {
    try {
      setLoading(true);
      
      if (type === 'income') {
        await apiCall('/Filters/quick/income', { method: 'POST' });
      } else {
        await apiCall('/Filters/quick/outcome', { method: 'POST' });
      }
      
      await loadFilters();
      await loadStats();
      alert(`Bộ lọc ${type === 'income' ? 'thu nhập' : 'chi tiêu'} đã được tạo thành công!`);
    } catch (error) {
      alert('Lỗi khi tạo bộ lọc: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Add new condition
  const addCondition = () => {
    setNewFilter(prev => ({
      ...prev,
      conditions: [
        ...prev.conditions,
        {
          id: (Date.now() + Math.random()).toString(),
          type: 'sender',
          operator: 'contains',
          value: ''
        }
      ]
    }));
  };

  // Remove condition
  const removeCondition = (id) => {
    if (newFilter.conditions.length === 1) return;
    setNewFilter(prev => ({
      ...prev,
      conditions: prev.conditions.filter(c => c.id !== id)
    }));
  };

  // Update condition
  const updateCondition = (id, field, value) => {
    setNewFilter(prev => ({
      ...prev,
      conditions: prev.conditions.map(c => 
        c.id === id ? { ...c, [field]: value } : c
      )
    }));
  };

  // Validation
  const validateFilter = (filter) => {
    const errors = {};
    
    if (!filter.name.trim()) {
      errors.name = 'Tên bộ lọc không được để trống';
    }

    const hasValidCondition = filter.conditions.some(c => c.value.trim());
    const hasGmailFilters = filter.gmailFilters.labels.length > 0 || 
                           filter.gmailFilters.hasAttachment || 
                           filter.customQuery.trim();

    if (!hasValidCondition && !hasGmailFilters && filter.type === 'custom') {
      errors.conditions = 'Phải có ít nhất một điều kiện lọc';
    }

    setFilterErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Test filter
  const testFilter = async () => {
    if (!sampleEmail.trim()) {
      alert('Vui lòng nhập nội dung email mẫu để test');
      return;
    }

    try {
      setLoading(true);
      
      // Prepare filter data for testing
      const filterData = {
        name: newFilter.name,
        description: newFilter.description || "",
        type: newFilter.type,
        templateId: newFilter.templateId || "",
        isActive: newFilter.isActive,
        matchCondition: newFilter.matchCondition,
        conditions: newFilter.conditions.map(c => ({
          id: c.id.toString(),
          type: c.type,
          operator: c.operator,
          value: c.value
        })),
        gmailFilters: {
          labels: newFilter.gmailFilters?.labels || [],
          hasAttachment: newFilter.gmailFilters?.hasAttachment || "",
          isRead: newFilter.gmailFilters?.isRead || "",
          isImportant: newFilter.gmailFilters?.isImportant || "",
          isStarred: newFilter.gmailFilters?.isStarred || "",
          dateRange: {
            type: newFilter.gmailFilters?.dateRange?.type || "any",
            value: newFilter.gmailFilters?.dateRange?.value || "",
            startDate: newFilter.gmailFilters?.dateRange?.startDate || null,
            endDate: newFilter.gmailFilters?.dateRange?.endDate || null
          }
        },
        extractionRules: newFilter.extractionRules || {},
        customQuery: newFilter.customQuery || ""
      };

      const result = await apiCall('/Filters/test', {
        method: 'POST',
        body: JSON.stringify({
          filter: filterData,
          sampleEmail: sampleEmail
        })
      });
      setTestResult(result);
    } catch (error) {
      console.error('Test filter error:', error);
      alert('Lỗi khi test bộ lọc: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Reset form
  const resetFilterForm = () => {
    setNewFilter({ 
      name: '', 
      description: '',
      type: 'custom',
      templateId: '',
      isActive: true,
      matchCondition: 'any',
      conditions: [
        {
          id: Date.now().toString(),
          type: 'sender',
          operator: 'contains',
          value: ''
        }
      ],
      gmailFilters: {
        labels: [],
        hasAttachment: '',
        isRead: '',
        isImportant: '',
        isStarred: '',
        dateRange: { type: 'any', value: '', startDate: null, endDate: null }
      },
      extractionRules: {},
      customQuery: ''
    });
    setFilterErrors({});
    setShowFilterForm(false);
    setEditingFilter(null);
    setSelectedTemplate(null);
    setShowTestDialog(false);
    setTestResult(null);
    setSampleEmail('');
  };

  // Save filter
  const saveFilter = async () => {
    if (!validateFilter(newFilter)) {
      return;
    }
    
    try {
      setLoading(true);
      
      // Prepare data according to API schema
      const filterData = {
        name: newFilter.name,
        description: newFilter.description || "",
        type: newFilter.type,
        templateId: newFilter.templateId || "",
        isActive: newFilter.isActive,
        matchCondition: newFilter.matchCondition,
        conditions: newFilter.conditions.map(c => ({
          id: c.id.toString(), // Ensure ID is string
          type: c.type,
          operator: c.operator,
          value: c.value
        })),
        gmailFilters: {
          labels: newFilter.gmailFilters?.labels || [],
          hasAttachment: newFilter.gmailFilters?.hasAttachment || "",
          isRead: newFilter.gmailFilters?.isRead || "",
          isImportant: newFilter.gmailFilters?.isImportant || "",
          isStarred: newFilter.gmailFilters?.isStarred || "",
          dateRange: {
            type: newFilter.gmailFilters?.dateRange?.type || "any",
            value: newFilter.gmailFilters?.dateRange?.value || "",
            startDate: newFilter.gmailFilters?.dateRange?.startDate || null,
            endDate: newFilter.gmailFilters?.dateRange?.endDate || null
          }
        },
        extractionRules: newFilter.extractionRules || {},
        customQuery: newFilter.customQuery || ""
      };

      console.log('Sending filter data:', filterData); // Debug log
      
      if (editingFilter) {
        await apiCall(`/Filters/${editingFilter.id}`, {
          method: 'PUT',
          body: JSON.stringify(filterData)
        });
        alert('Bộ lọc đã được cập nhật thành công!');
      } else {
        await apiCall('/Filters', {
          method: 'POST',
          body: JSON.stringify(filterData)
        });
        alert('Bộ lọc đã được tạo thành công!');
      }
      
      await loadFilters();
      await loadStats();
      resetFilterForm();
      
    } catch (error) {
      console.error('Save filter error:', error);
      alert('Lỗi khi lưu bộ lọc: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Edit filter
  const editFilter = (filter) => {
    setNewFilter({ ...filter });
    setEditingFilter(filter);
    setShowFilterForm(true);
    setFilterErrors({});
  };

  // Delete filter
  const deleteFilter = async (filterId) => {
    if (!confirm('Bạn có chắc chắn muốn xóa bộ lọc này?')) {
      return;
    }
    
    try {
      setLoading(true);
      await apiCall(`/Filters/${filterId}`, { method: 'DELETE' });
      await loadFilters();
      await loadStats();
      alert('Bộ lọc đã được xóa thành công!');
    } catch (error) {
      alert('Lỗi khi xóa bộ lọc: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const getFilterTypeIcon = (type) => {
    switch(type) {
      case 'income': return <ArrowDownCircle className="h-4 w-4 text-green-600" />;
      case 'outcome': return <ArrowUpCircle className="h-4 w-4 text-red-600" />;
      case 'template': return <Zap className="h-4 w-4 text-purple-600" />;
      default: return <Filter className="h-4 w-4 text-blue-600" />;
    }
  };

  const getFilterTypeBadge = (type) => {
    const badges = {
      income: 'bg-green-100 text-green-800',
      outcome: 'bg-red-100 text-red-800', 
      template: 'bg-purple-100 text-purple-800',
      custom: 'bg-blue-100 text-blue-800'
    };
    
    const labels = {
      income: 'Thu nhập',
      outcome: 'Chi tiêu',
      template: 'Template',
      custom: 'Tùy chỉnh'
    };

    return (
      <span className={`inline-flex px-2 py-1 text-xs rounded-full ${badges[type] || badges.custom}`}>
        {labels[type] || labels.custom}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      <div className="w-full bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Activity className="h-6 w-6 mr-2 text-blue-600" />
            Quản lý bộ lọc email thông minh
          </h2>
          <div className="flex space-x-3">
            <button
              onClick={() => createQuickFilter('income')}
              disabled={loading}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
            >
              <ArrowDownCircle className="h-4 w-4" />
              <span>Thu nhập</span>
            </button>
            <button
              onClick={() => createQuickFilter('outcome')}
              disabled={loading}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
            >
              <ArrowUpCircle className="h-4 w-4" />
              <span>Chi tiêu</span>
            </button>
            <button
              onClick={() => setShowFilterForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Tùy chỉnh</span>
            </button>
          </div>
        </div>

        {/* Stats Dashboard */}
        {filterStats && (
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4 mb-8">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-blue-600">{filterStats.totalFilters}</div>
              <div className="text-sm text-blue-700">Tổng bộ lọc</div>
            </div>
            <div className="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-green-600">{filterStats.incomeFilters}</div>
              <div className="text-sm text-green-700">Thu nhập</div>
            </div>
            <div className="bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-red-600">{filterStats.outcomeFilters}</div>
              <div className="text-sm text-red-700">Chi tiêu</div>
            </div>
            <div className="bg-gradient-to-r from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-purple-600">{filterStats.templateFilters}</div>
              <div className="text-sm text-purple-700">Templates</div>
            </div>
            <div className="bg-gradient-to-r from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-orange-600">{filterStats.totalExtractionFields}</div>
              <div className="text-sm text-orange-700">Data Fields</div>
            </div>
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200 rounded-lg p-4">
              <div className="text-2xl font-bold text-gray-600">{filterStats.activeFilters}</div>
              <div className="text-sm text-gray-700">Hoạt động</div>
            </div>
          </div>
        )}

        {/* Filter Form Modal */}
        {showFilterForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl max-w-6xl w-full max-h-[95vh] overflow-hidden flex flex-col">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h2 className="text-2xl font-bold text-gray-900">
                  {editingFilter ? 'Chỉnh sửa bộ lọc' : 'Tạo bộ lọc mới'}
                </h2>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowTestDialog(true)}
                    className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center space-x-2"
                  >
                    <TestTube className="h-4 w-4" />
                    <span>Test</span>
                  </button>
                  <button onClick={resetFilterForm} className="text-gray-400 hover:text-gray-600">
                    <X className="h-6 w-6" />
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-6">
                {/* Basic Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tên bộ lọc *
                    </label>
                    <input
                      type="text"
                      value={newFilter.name}
                      onChange={(e) => setNewFilter(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="VD: LianLian Payment Detection"
                      className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        filterErrors.name ? 'border-red-500' : 'border-gray-300'
                      }`}
                    />
                    {filterErrors.name && <p className="text-red-500 text-xs mt-1">{filterErrors.name}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Loại bộ lọc
                    </label>
                    <select
                      value={newFilter.type}
                      onChange={(e) => setNewFilter(prev => ({ ...prev, type: e.target.value }))}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="custom">Tùy chỉnh</option>
                      <option value="income">Thu nhập (Smart)</option>
                      <option value="outcome">Chi tiêu (Smart)</option>
                      <option value="template">Từ Template</option>
                    </select>
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Mô tả
                    </label>
                    <textarea
                      value={newFilter.description}
                      onChange={(e) => setNewFilter(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Mô tả chi tiết về bộ lọc này..."
                      rows={2}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                {/* Template Selection */}
                {newFilter.type === 'template' && emailTemplates.length > 0 && (
                  <div className="mb-8">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Chọn Template</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {emailTemplates.map(template => (
                        <div 
                          key={template.id}
                          onClick={() => applyTemplate(template)}
                          className={`border rounded-lg p-4 cursor-pointer transition-all ${
                            selectedTemplate?.id === template.id 
                              ? 'border-blue-500 bg-blue-50' 
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="flex items-center space-x-3 mb-2">
                            {template.type === 'income' ? 
                              <ArrowDownCircle className="h-5 w-5 text-green-600" /> : 
                              <ArrowUpCircle className="h-5 w-5 text-red-600" />
                            }
                            <div>
                              <h4 className="font-medium text-gray-900">{template.name}</h4>
                              <p className="text-sm text-gray-500">{template.platform}</p>
                            </div>
                          </div>
                          
                          <div className="text-xs text-gray-600">
                            <p>Language: {template.language}</p>
                            <p>Extract: {Object.keys(template.extractionRules || {}).slice(0, 3).join(', ')}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Conditions */}
                {(newFilter.type === 'custom' || newFilter.type === 'template') && (
                  <div className="mb-8">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">Điều kiện lọc</h3>
                      <button
                        onClick={addCondition}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                      >
                        <Plus className="h-4 w-4" />
                        <span>Thêm điều kiện</span>
                      </button>
                    </div>

                    {filterErrors.conditions && (
                      <p className="text-red-500 text-sm mb-4">{filterErrors.conditions}</p>
                    )}

                    <div className="space-y-4">
                      {newFilter.conditions.map((condition, index) => (
                        <div key={condition.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                          <div className="flex items-center justify-between mb-4">
                            <span className="text-sm font-medium text-gray-700">
                              Điều kiện {index + 1}
                            </span>
                            {newFilter.conditions.length > 1 && (
                              <button
                                onClick={() => removeCondition(condition.id)}
                                className="text-red-600 hover:text-red-800"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            )}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Loại
                              </label>
                              <select
                                value={condition.type}
                                onChange={(e) => updateCondition(condition.id, 'type', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              >
                                {conditionTypes.map(type => (
                                  <option key={type.value} value={type.value}>
                                    {type.label}
                                  </option>
                                ))}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Toán tử
                              </label>
                              <select
                                value={condition.operator}
                                onChange={(e) => updateCondition(condition.id, 'operator', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              >
                                {operators.map(op => (
                                  <option key={op.value} value={op.value}>
                                    {op.label}
                                  </option>
                                ))}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Giá trị
                              </label>
                              <input
                                type="text"
                                value={condition.value}
                                onChange={(e) => updateCondition(condition.id, 'value', e.target.value)}
                                placeholder={
                                  condition.type === 'sender' ? 'VD: paypal.com' :
                                  condition.type === 'title' ? 'VD: Payment confirmation' :
                                  'VD: You have received'
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Extraction Rules Preview */}
                {(newFilter.type === 'income' || newFilter.type === 'outcome' || newFilter.type === 'template') && 
                 newFilter.extractionRules && Object.keys(newFilter.extractionRules).length > 0 && (
                  <div className="mb-8">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Quy tắc trích xuất dữ liệu</h3>
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <h4 className="font-medium text-green-800 mb-2">
                        🎯 Dữ liệu sẽ được trích xuất tự động:
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {Object.keys(newFilter.extractionRules).map(field => (
                          <span key={field} className="inline-flex px-2 py-1 bg-green-100 text-green-800 text-sm rounded">
                            {field}
                          </span>
                        ))}
                      </div>
                      <p className="text-sm text-green-600 mt-2">
                        Hệ thống sẽ tự động trích xuất các thông tin này từ email phù hợp
                      </p>
                    </div>
                  </div>
                )}

                {/* Preview */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-800 mb-2 flex items-center">
                    <Eye className="h-4 w-4 mr-2" />
                    Xem trước bộ lọc:
                  </h4>
                  <div className="text-sm bg-white p-3 rounded border">
                    <div className="flex items-center space-x-2 mb-2">
                      {getFilterTypeIcon(newFilter.type)}
                      <span className="font-medium text-gray-700">{newFilter.name || 'Chưa nhập tên'}</span>
                      {getFilterTypeBadge(newFilter.type)}
                    </div>
                    
                    {newFilter.conditions.filter(c => c.value && c.value.trim()).length > 0 && (
                      <div className="mt-2">
                        <span className="font-medium text-gray-700">Điều kiện:</span>
                        <ul className="list-disc list-inside mt-1 space-y-1">
                          {newFilter.conditions.filter(c => c.value && c.value.trim()).map((condition, index) => (
                            <li key={index} className="text-gray-600 text-sm">
                              {conditionTypes.find(t => t.value === condition.type)?.label} {' '}
                              {operators.find(o => o.value === condition.operator)?.label} "{condition.value}"
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {newFilter.description && (
                      <div className="text-gray-600 mt-2 italic border-t pt-2">
                        {newFilter.description}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="border-t border-gray-200 p-6 bg-gray-50">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="isActive"
                      checked={newFilter.isActive}
                      onChange={(e) => setNewFilter(prev => ({ ...prev, isActive: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="isActive" className="text-sm text-gray-700">
                      Kích hoạt bộ lọc sau khi lưu
                    </label>
                  </div>

                  <div className="flex space-x-4">
                    <button
                      onClick={resetFilterForm}
                      className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Hủy
                    </button>
                    <button
                      onClick={saveFilter}
                      disabled={loading}
                      className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
                    >
                      <Save className="h-4 w-4" />
                      <span>{editingFilter ? 'Cập nhật' : 'Lưu bộ lọc'}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Test Dialog */}
        {showTestDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
            <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-900 flex items-center">
                  <TestTube className="h-5 w-5 mr-2 text-orange-600" />
                  Test bộ lọc
                </h2>
                <button onClick={() => setShowTestDialog(false)} className="text-gray-400 hover:text-gray-600">
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="flex-1 overflow-y-auto p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nội dung email mẫu để test:
                    </label>
                    <textarea
                      value={sampleEmail}
                      onChange={(e) => setSampleEmail(e.target.value)}
                      placeholder="Dán nội dung email mẫu vào đây..."
                      rows={8}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div className="flex space-x-4">
                    <button
                      onClick={testFilter}
                      disabled={loading || !sampleEmail.trim()}
                      className="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
                    >
                      <TestTube className="h-4 w-4" />
                      <span>Chạy test</span>
                    </button>
                  </div>

                  {testResult && (
                    <div className="mt-6 p-4 border rounded-lg">
                      <h3 className="font-semibold text-lg mb-4 flex items-center">
                        {testResult.matches ? (
                          <Check className="h-5 w-5 text-green-600 mr-2" />
                        ) : (
                          <X className="h-5 w-5 text-red-600 mr-2" />
                        )}
                        Kết quả test
                      </h3>

                      <div className={`p-3 rounded-lg mb-4 ${
                        testResult.matches ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                      }`}>
                        <p className={`font-medium ${testResult.matches ? 'text-green-800' : 'text-red-800'}`}>
                          {testResult.matches ? '✅ Bộ lọc khớp với email mẫu!' : '❌ Bộ lọc không khớp với email mẫu'}
                        </p>
                        <p className={`text-sm mt-1 ${testResult.matches ? 'text-green-700' : 'text-red-700'}`}>
                          {testResult.explanation}
                        </p>
                      </div>

                      {testResult.extractedData && Object.keys(testResult.extractedData).length > 0 && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                          <h4 className="font-medium text-blue-800 mb-2">Dữ liệu được trích xuất:</h4>
                          <div className="space-y-2">
                            {Object.entries(testResult.extractedData).map(([key, value]) => (
                              <div key={key} className="flex justify-between items-center bg-white p-2 rounded border">
                                <span className="font-medium text-gray-700">{key}:</span>
                                <span className="text-gray-900">{value?.toString()}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Filters List */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-900">
              📧 Danh sách bộ lọc ({filters.length})
            </h3>
            <button
              onClick={loadFilters}
              disabled={loading}
              className="bg-blue-100 text-blue-700 px-4 py-2 rounded-lg hover:bg-blue-200 transition-colors disabled:opacity-50 flex items-center space-x-2"
            >
              <Search className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
          
          {filters.map((filter) => (
            <div key={filter.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    {getFilterTypeIcon(filter.type)}
                    <h4 className="font-semibold text-gray-900">{filter.name}</h4>
                    <span className={`inline-flex px-2 py-1 text-xs rounded-full ${
                      filter.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {filter.isActive ? 'Hoạt động' : 'Tạm dừng'}
                    </span>
                    {getFilterTypeBadge(filter.type)}
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    {/* Display filter conditions */}
                    {filter.conditions && filter.conditions.length > 0 && (
                      <div>
                        <span className="font-medium text-gray-700">Điều kiện:</span>
                        <div className="mt-1 space-y-1">
                          {filter.conditions.filter(c => c.value && c.value.trim()).map((condition, index) => (
                            <div key={index} className="bg-white p-2 rounded border text-xs">
                              <span className="font-medium">
                                {conditionTypes.find(t => t.value === condition.type)?.label}
                              </span>
                              {' '}
                              <span className="text-gray-600">
                                {operators.find(o => o.value === condition.operator)?.label}
                              </span>
                              {' '}
                              <span className="font-mono bg-gray-100 px-1 rounded">
                                "{condition.value}"
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Display extraction rules */}
                    {filter.extractionRules && Object.keys(filter.extractionRules).length > 0 && (
                      <div>
                        <span className="font-medium text-gray-700">Trích xuất:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {Object.keys(filter.extractionRules).map(field => (
                            <span key={field} className="inline-flex px-2 py-1 rounded text-xs bg-green-100 text-green-800">
                              {field}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {filter.description && (
                      <p className="text-gray-600 italic">{filter.description}</p>
                    )}
                    
                    {filter.createdAt && (
                      <p className="text-xs text-gray-500">
                        Tạo: {new Date(filter.createdAt).toLocaleString('vi-VN')}
                        {filter.updatedAt && filter.updatedAt !== filter.createdAt && (
                          <span className="ml-2">
                            • Cập nhật: {new Date(filter.updatedAt).toLocaleString('vi-VN')}
                          </span>
                        )}
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="flex space-x-2 ml-4">
                  <button
                    onClick={() => editFilter(filter)}
                    className="bg-blue-100 text-blue-700 p-2 rounded-lg hover:bg-blue-200 transition-colors"
                    title="Chỉnh sửa"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => deleteFilter(filter.id)}
                    className="bg-red-100 text-red-700 p-2 rounded-lg hover:bg-red-200 transition-colors"
                    title="Xóa"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
          
          {filters.length === 0 && !loading && (
            <div className="text-center py-12 text-gray-500">
              <Filter className="h-16 w-16 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">Chưa có bộ lọc nào</p>
              <p className="text-sm mb-4">Tạo bộ lọc thông minh để tự động trích xuất dữ liệu email</p>
              <div className="flex justify-center space-x-3">
                <button
                  onClick={() => createQuickFilter('income')}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
                >
                  <ArrowDownCircle className="h-4 w-4" />
                  <span>Tạo bộ lọc Thu nhập</span>
                </button>
                <button
                  onClick={() => createQuickFilter('outcome')}
                  className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
                >
                  <ArrowUpCircle className="h-4 w-4" />
                  <span>Tạo bộ lọc Chi tiêu</span>
                </button>
              </div>
            </div>
          )}
          
          {loading && (
            <div className="text-center py-8">
              <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-blue-500 bg-blue-100">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Đang tải...
              </div>
            </div>
          )}
        </div>

        {/* Templates Library */}
        {emailTemplates.length > 0 && (
          <div className="mt-8 bg-gray-50 rounded-lg p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Zap className="h-5 w-5 mr-2" />
              Thư viện Templates có sẵn
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {emailTemplates.map(template => (
                <div key={template.id} className="bg-white rounded-lg p-4 border border-gray-200 hover:shadow-md transition-shadow">
                  <div className="flex items-center space-x-2 mb-2">
                    {template.type === 'income' ? 
                      <ArrowDownCircle className="h-4 w-4 text-green-600" /> : 
                      <ArrowUpCircle className="h-4 w-4 text-red-600" />
                    }
                    <span className="font-medium text-sm">{template.platform}</span>
                    <span className="text-xs">{template.language === 'vi' ? '🇻🇳' : template.language === 'en' ? '🇺🇸' : '🇺🇸🇻🇳'}</span>
                  </div>
                  <h4 className="font-medium text-gray-900 text-sm mb-2">{template.name}</h4>
                  <div className="text-xs text-gray-600 mb-3">
                    Extract: {Object.keys(template.extractionRules || {}).slice(0, 2).join(', ')}
                    {Object.keys(template.extractionRules || {}).length > 2 && '...'}
                  </div>
                  <button
                    onClick={() => {
                      applyTemplate(template);
                      setShowFilterForm(true);
                    }}
                    className="w-full bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 transition-colors"
                  >
                    Sử dụng Template
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Platform Support Info */}
        {filterStats && filterStats.supportedPlatforms && (
          <div className="mt-6 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-4 border border-indigo-200">
            <h4 className="font-medium text-indigo-800 mb-3">Platforms được hỗ trợ:</h4>
            <div className="flex flex-wrap gap-2">
              {filterStats.supportedPlatforms.map(platform => (
                <span key={platform} className="inline-flex px-3 py-1 bg-indigo-100 text-indigo-800 text-sm rounded-full">
                  {platform}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default IntegratedFilterWithAPI;