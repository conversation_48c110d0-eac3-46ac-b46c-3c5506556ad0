# 🔄 Auto-Refresh Token System

## Tổng quan
Hệ thống tự động làm mới token đã được triển khai để đảm bảo các tài khoản Gmail luôn hoạt động mà không cần can thiệp thủ công.

## ✨ Tính năng chính

### 1. **Tự động phát hiện token hết hạn**
- Kiểm tra trạng thái token trong mọi API call
- Phát hiện token hết hạn qua error messages
- Tự động trigger refresh process

### 2. **Auto-refresh thông minh**
- **Immediate refresh**: Khi token đã hết hạn
- **Proactive refresh**: Khi token sắp hết hạn (trong vòng 7 ngày)
- **Periodic check**: Kiểm tra định kỳ mỗi 6 giờ
- **Hourly monitoring**: Cảnh báo token sắp hết hạn mỗi giờ

### 3. **Notification system**
- Thông báo real-time khi refresh thành công
- <PERSON><PERSON><PERSON> báo khi refresh thất bại
- Toast notifications với auto-dismiss
- Console logging chi tiết

### 4. **Visual indicators**
- Nút refresh có màu sắc khác nhau theo trạng thái:
  - 🟢 **Xanh**: Token khỏe mạnh
  - 🟡 **Vàng**: Token sắp hết hạn (animate pulse)
  - 🔴 **Đỏ**: Token đã hết hạn (animate pulse)
- Hiển thị ngày hết hạn token
- Status badges với icon phù hợp

## 🔧 Cách hoạt động

### API Level Auto-Refresh
```javascript
// Trong utils/api.js
export const apiCall = async (endpoint, options = {}) => {
  try {
    // Thực hiện API call
    const response = await fetch(url, options);
    
    if (!response.ok) {
      // Phát hiện token expiration
      const accountId = extractAccountId(endpoint, options);
      return await handleTokenExpiration(error, endpoint, options, accountId);
    }
    
    return data;
  } catch (error) {
    // Auto-retry với token mới nếu refresh thành công
    throw error;
  }
};
```

### Component Level Monitoring
```javascript
// Trong AccountsTab.jsx
useEffect(() => {
  const checkAndRefreshTokens = async () => {
    for (const account of accounts) {
      const status = getAccountStatus(account);
      
      if (status.needsRefresh && account.tokenExpired) {
        await refreshToken(account.id);
      }
    }
  };

  // Kiểm tra ngay lập tức
  checkAndRefreshTokens();
  
  // Kiểm tra định kỳ mỗi 6 giờ
  const interval = setInterval(checkAndRefreshTokens, 6 * 60 * 60 * 1000);
  
  return () => clearInterval(interval);
}, [accounts]);
```

## 📊 Trạng thái Token

### Token Status Logic
```javascript
const getAccountStatus = (account) => {
  if (!account.isActive) {
    return { icon: AlertCircle, color: 'bg-red-100', text: 'Đã tắt' };
  } 
  
  if (account.tokenExpired) {
    return { 
      icon: AlertCircle, 
      color: 'bg-red-100', 
      text: 'Token hết hạn', 
      needsRefresh: true 
    };
  }
  
  if (account.tokenExpiresAt) {
    const daysUntilExpiry = calculateDaysUntilExpiry(account.tokenExpiresAt);
    
    if (daysUntilExpiry <= 7 && daysUntilExpiry > 0) {
      return { 
        icon: Clock, 
        color: 'bg-yellow-100', 
        text: `Hết hạn trong ${daysUntilExpiry} ngày`, 
        needsRefresh: true 
      };
    }
  }
  
  return { icon: CheckCircle, color: 'bg-green-100', text: 'Đã kết nối' };
};
```

## 🔔 Notification Types

### Success Notification
```javascript
showTokenRefreshSuccess(accountEmail);
// ✅ <NAME_EMAIL> đã được làm mới thành công
```

### Error Notification  
```javascript
showTokenRefreshError(accountEmail, error);
// ❌ Không thể làm mới <NAME_EMAIL>: Invalid credentials
```

### Auto-refresh Started
```javascript
showAutoRefreshStarted(accountEmail);
// 🔄 Đang tự động làm mới <NAME_EMAIL>...
```

### Expiry Warning
```javascript
showTokenExpiryWarning(accountEmail, hoursLeft);
// ⚠️ <NAME_EMAIL> sẽ hết hạn trong 12 giờ
```

## 🎯 Lợi ích

### Cho người dùng:
- ✅ Không cần can thiệp thủ công
- ✅ Giảm downtime của hệ thống
- ✅ Thông báo rõ ràng về trạng thái
- ✅ Trải nghiệm mượt mà

### Cho hệ thống:
- ✅ Tự động recovery từ token expiration
- ✅ Proactive maintenance
- ✅ Detailed logging và monitoring
- ✅ Graceful error handling

## 🔧 Configuration

### Timing Settings
```javascript
// Kiểm tra định kỳ mỗi 6 giờ
const AUTO_REFRESH_INTERVAL = 6 * 60 * 60 * 1000;

// Cảnh báo khi token hết hạn trong 7 ngày
const WARNING_DAYS_THRESHOLD = 7;

// Kiểm tra expiry warning mỗi giờ
const EXPIRY_CHECK_INTERVAL = 60 * 60 * 1000;

// Auto-dismiss success notifications sau 5 giây
const SUCCESS_NOTIFICATION_TIMEOUT = 5000;
```

## 🚀 Cách sử dụng

1. **Automatic**: Hệ thống tự động hoạt động, không cần setup
2. **Manual**: Click nút refresh để làm mới token thủ công
3. **Monitoring**: Theo dõi notifications ở góc phải màn hình
4. **Status**: Xem trạng thái token trong danh sách accounts

## 🔍 Troubleshooting

### Nếu auto-refresh thất bại:
1. Kiểm tra kết nối internet
2. Verify API endpoint hoạt động
3. Check account permissions
4. Xem console logs để debug
5. Thử refresh thủ công

### Logs để debug:
```javascript
// Success
console.log(`✅ Token auto-refreshed for account ${accountId}`);

// Error  
console.error(`❌ Auto-refresh failed for account ${accountId}:`, error);

// Warning
console.warn(`⚠️ Token for ${account.email} expires in ${hoursUntilExpiry} hours`);
```

## 📈 Monitoring

Hệ thống cung cấp monitoring chi tiết:
- Real-time status trong UI
- Console logging
- Toast notifications
- Visual indicators
- Periodic health checks

---

**Lưu ý**: Tính năng này đảm bảo hệ thống hoạt động liên tục mà không cần can thiệp thủ công, tăng độ tin cậy và trải nghiệm người dùng.
