import React, { useState, useEffect } from 'react';
import { Mail, RefreshCw, LogOut, User, Lock } from 'lucide-react';

import Navigation from './Navigation';
import AccountsTab from './AccountsTab';
import FiltersTab from './FiltersTab';
import ScanTab from './ScanTab';
import DataTab from './DataTab';

// Import API utilities
import { apiCall } from '../utils/api';

const LoginForm = ({ onLogin, loading }) => {
  const [credentials, setCredentials] = useState({ username: '', password: '' });
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    
    if (!credentials.username || !credentials.password) {
      setError('Vui lòng nhập đầy đủ thông tin');
      return;
    }

    try {
      await onLogin(credentials);
    } catch (error) {
      setError(error.message || '<PERSON><PERSON>ng nhập thất bại');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl p-8 w-full max-w-md">
        <div className="text-center mb-8">
          <img src={LSBLogo} alt="LSB Logo" className="h-16 w-16 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900">LSB Email Manager</h1>
          <p className="text-gray-600">Đăng nhập để tiếp tục</p>
        </div>

        <div className="space-y-6">
          <div>
            <div className="block text-sm font-medium text-gray-700 mb-2">
              Tên đăng nhập
            </div>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                value={credentials.username}
                onChange={(e) => setCredentials({ ...credentials, username: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Nhập tên đăng nhập"
                disabled={loading}
                onKeyDown={(e) => e.key === 'Enter' && handleSubmit(e)}
              />
            </div>
          </div>

          <div>
            <div className="block text-sm font-medium text-gray-700 mb-2">
              Mật khẩu
            </div>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="password"
                value={credentials.password}
                onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Nhập mật khẩu"
                disabled={loading}
                onKeyDown={(e) => e.key === 'Enter' && handleSubmit(e)}
              />
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          <button
            onClick={handleSubmit}
            disabled={loading}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white font-medium py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center space-x-2"
          >
            {loading ? (
              <>
                <RefreshCw className="h-5 w-5 animate-spin" />
                <span>Đang đăng nhập...</span>
              </>
            ) : (
              <span>Đăng nhập</span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

const LSBSellerMailTrack = () => {
  const [user, setUser] = useState(null);
  const [accounts, setAccounts] = useState([]);
  const [filters, setFilters] = useState([]);
  const [scanResults, setScanResults] = useState([]);
  const [activeTab, setActiveTab] = useState(() => {
    return localStorage.getItem('lsb-active-tab') || 'accounts';
  });
  const [loading, setLoading] = useState(false);
  const [extractedData, setExtractedData] = useState([]);
  const [lastScanResponse, setLastScanResponse] = useState(null);

  useEffect(() => {
    const token = localStorage.getItem('lsb-token');
    const userData = localStorage.getItem('lsb-user');
    
    if (token && userData) {
      try {
        setUser(JSON.parse(userData));
      } catch (error) {
        localStorage.removeItem('lsb-token');
        localStorage.removeItem('lsb-user');
      }
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('lsb-active-tab', activeTab);
  }, [activeTab]);

  useEffect(() => {
    if (user) {
      loadAccounts();
      loadFilters();
    }
  }, [user]);

  const handleLogin = async (credentials) => {
    try {
      setLoading(true);
      const response = await apiCall('/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials)
      });

      if (response.token && response.user) {
        localStorage.setItem('lsb-token', response.token);
        localStorage.setItem('lsb-user', JSON.stringify(response.user));
        setUser(response.user);
      } else {
        throw new Error('Phản hồi từ server không hợp lệ');
      }
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('lsb-token');
    localStorage.removeItem('lsb-user');
    localStorage.removeItem('lsb-active-tab');
    setUser(null);
    setAccounts([]);
    setFilters([]);
    setExtractedData([]);
    setScanResults([]);
    setLastScanResponse(null);
  };

  const loadAccounts = async () => {
    try {
      setLoading(true);
      const data = await apiCall('/Accounts');
      setAccounts(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Load accounts error:', error);
      alert('Lỗi khi tải danh sách tài khoản: ' + error.message);
      setAccounts([]);
    } finally {
      setLoading(false);
    }
  };

  const loadFilters = async () => {
    try {
      setLoading(true);
      const data = await apiCall('/filters');
      setFilters(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Load filters error:', error);
      alert('Lỗi khi tải danh sách bộ lọc: ' + error.message);
      setFilters([]);
    } finally {
      setLoading(false);
    }
  };

  const calculateTotal = () => {
    return extractedData.reduce((total, item) => {
      const amount = parseFloat(item.amount || 0);
      return total + amount;
    }, 0);
  };

  const clearProcessedEmails = async () => {
    if (!confirm('Bạn có chắc chắn muốn xóa tất cả dữ liệu đã xử lý?')) {
      return;
    }
    
    try {
      setLoading(true);
      await apiCall('/clear-processed-emails', { method: 'POST' });
      setExtractedData([]);
      setScanResults([]);
      setLastScanResponse(null);
      alert('Đã xóa tất cả dữ liệu thành công!');
    } catch (error) {
      console.error('Clear processed emails error:', error);
      alert('Lỗi khi xóa dữ liệu: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const batchProcessAccounts = async (accountIds, fromDate, toDate) => {
    try {
      setLoading(true);
      const response = await apiCall('/batch-process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accountIds,
          fromDate,
          toDate
        })
      });
      
      return response;
    } catch (error) {
      console.error('Batch process error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const getAccountStats = async (accountId) => {
    try {
      const stats = await apiCall(`/Accounts/${accountId}/stats`);
      return stats;
    } catch (error) {
      console.error(`Failed to get stats for account ${accountId}:`, error);
      return null;
    }
  };

  const exportAllData = async () => {
    try {
      setLoading(true);
      const response = await apiCall('/export-all');
      
      const blob = new Blob([JSON.stringify(response, null, 2)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `lsb-seller-data-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      alert('Dữ liệu đã được xuất thành công!');
    } catch (error) {
      console.error('Export all data error:', error);
      alert('Lỗi khi xuất dữ liệu: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return <LoginForm onLogin={handleLogin} loading={loading} />;
  }

  const sharedProps = {
    loading,
    setLoading,
    accounts,
    filters,
    extractedData,
    setExtractedData,
    scanResults,
    setScanResults,
    lastScanResponse,
    setLastScanResponse,
    loadAccounts,
    loadFilters,
    calculateTotal,
    clearProcessedEmails,
    batchProcessAccounts,
    getAccountStats,
    exportAllData,
    setActiveTab,
    user
  };

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'accounts':
        return <AccountsTab {...sharedProps} />;
      case 'filters':
        return <FiltersTab {...sharedProps} />;
      case 'scan':
        return <ScanTab {...sharedProps} />;
      case 'data':
        return <DataTab {...sharedProps} />;
      default:
        return <AccountsTab {...sharedProps} />;
    }
  };

  return (
    <div className="min-h-screen w-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="w-full bg-white shadow-lg border-b">
        <div className="w-full px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-lg">
                <img 
                  src={LSBLogo} 
                  alt="LSB Logo" 
                  className="h-12 w-12 object-contain"
                />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">LSB Email Manager</h1>
                <p className="text-sm text-gray-600">Xin chào, {user.username}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-6">
              {loading && (
                <div className="flex items-center space-x-2 text-blue-600">
                  <RefreshCw className="h-5 w-5 animate-spin" />
                  <span>Đang xử lý...</span>
                </div>
              )}
              
              <div className="text-right">
                <p className="text-sm text-gray-500">Tài khoản đã kết nối</p>
                <p className="text-2xl font-bold text-blue-600">{accounts.length}</p>
              </div>
              
              {extractedData.length > 0 && (
                <div className="text-right">
                  <p className="text-sm text-gray-500">Tổng số tiền</p>
                  <p className="text-2xl font-bold text-green-600">
                    {calculateTotal().toLocaleString('vi-VN')} USD
                  </p>
                </div>
              )}

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition duration-200"
              >
                <LogOut className="h-5 w-5" />
                <span>Đăng xuất</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <Navigation activeTab={activeTab} setActiveTab={setActiveTab} />

      <div className="w-full px-8 py-8">
        {renderActiveTab()}
      </div>

      {/* Global Loading Overlay */}
      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-20 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 flex items-center space-x-4">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-gray-700">Đang xử lý...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default LSBSellerMailTrack;