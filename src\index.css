:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* DataTab Responsive Variables */
  --container-max-width: 1920px;
  --sidebar-width: 280px;
  --header-height: 64px;
  --border-radius: 12px;
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.15);
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Theme Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
    
    /* Light theme overrides */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* Dark theme colors */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1f2937;
    --bg-secondary: #111827;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --border-color: #374151;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.3);
    --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.4);
  }
}

/* ==================================================
   RESPONSIVE STYLES FOR DATATAB COMPONENT
   Optimized for Desktop & Laptop Screens
   ================================================== */

/* Ultra Wide Screens (2560px+) */
@media (min-width: 2560px) {
  .data-tab-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 2rem 3rem;
  }
  
  .datatab-summary-grid {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .datatab-table {
    font-size: 16px;
  }
}

/* Large Desktop (1920px - 2559px) */
@media (min-width: 1920px) and (max-width: 2559px) {
  .data-tab-container {
    max-width: 1800px;
    margin: 0 auto;
    padding: 1.5rem 2rem;
  }
  
  .datatab-exchange-grid {
    grid-template-columns: repeat(5, 1fr);
  }
  
  .datatab-filter-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Standard Desktop (1440px - 1919px) */
@media (min-width: 1440px) and (max-width: 1919px) {
  .data-tab-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1.5rem;
  }
  
  .datatab-summary-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 1.5rem;
  }
  
  .datatab-currency-breakdown {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .datatab-table {
    font-size: 15px;
  }
}

/* Laptop Large (1366px - 1439px) */
@media (min-width: 1366px) and (max-width: 1439px) {
  .data-tab-container {
    max-width: 1320px;
    margin: 0 auto;
    padding: 1rem 1.5rem;
  }
  
  .datatab-summary-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
  }
  
  .datatab-exchange-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .datatab-filter-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .datatab-statistics-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Laptop Standard (1024px - 1365px) */
@media (min-width: 1024px) and (max-width: 1365px) {
  .data-tab-container {
    max-width: 100%;
    padding: 1rem;
  }
  
  .datatab-summary-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }
  
  .datatab-exchange-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .datatab-filter-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .datatab-currency-breakdown {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .datatab-table {
    font-size: 14px;
  }
  
  .datatab-table th {
    padding: 0.75rem 0.5rem;
    font-size: 13px;
  }
  
  .datatab-table td {
    padding: 0.75rem 0.5rem;
    font-size: 13px;
  }
}

/* Small Laptop (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .data-tab-container {
    padding: 0.75rem;
  }
  
  .datatab-summary-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
  
  .datatab-exchange-grid {
    grid-template-columns: 1fr;
  }
  
  .datatab-filter-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .datatab-currency-breakdown {
    grid-template-columns: 1fr;
  }
  
  .datatab-statistics-grid {
    grid-template-columns: 1fr;
  }
  
  .datatab-table {
    font-size: 13px;
  }
  
  /* Hide less important columns on smaller screens */
  .datatab-table .col-hide-sm {
    display: none;
  }
}

/* ==================================================
   DATATAB COMPONENT STYLES
   ================================================== */

.datatab-main {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  background: var(--bg-secondary);
  min-height: 100vh;
  padding: 1rem;
}

.datatab-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  transition: var(--transition-smooth);
  overflow: hidden;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
}

.datatab-card:hover {
  box-shadow: var(--shadow-heavy);
  transform: translateY(-2px);
}

.datatab-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-primary);
}

.datatab-card-body {
  padding: 1.5rem;
}

/* Table Styles */
.datatab-table-container {
  width: 100%;
  overflow-x: auto;
  border-radius: var(--border-radius);
  background: var(--bg-primary);
}

.datatab-table {
  width: 100%;
  min-width: 800px;
  border-collapse: collapse;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.datatab-table thead {
  background: var(--bg-secondary);
  position: sticky;
  top: 0;
  z-index: 10;
}

.datatab-table th {
  padding: 1rem 0.75rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  white-space: nowrap;
}

.datatab-table td {
  padding: 1rem 0.75rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.datatab-table tbody tr:hover {
  background: var(--bg-secondary);
}

/* Column Widths */
.datatab-table .col-date { width: 120px; }
.datatab-table .col-subject { width: 25%; min-width: 200px; }
.datatab-table .col-email { width: 20%; min-width: 150px; }
.datatab-table .col-amount { width: 130px; }
.datatab-table .col-amount-usd { width: 130px; }
.datatab-table .col-account { width: 15%; min-width: 120px; }
.datatab-table .col-filter { width: 120px; }
.datatab-table .col-actions { width: 100px; }

/* Button Styles */
.datatab-btn {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  transition: var(--transition-smooth);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
  border: 1px solid transparent;
  cursor: pointer;
  font-family: inherit;
}

.datatab-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

/* Input Styles */
.datatab-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 14px;
  transition: var(--transition-smooth);
  background: var(--bg-primary);
  color: var(--text-primary);
}

.datatab-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.datatab-select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,<svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236b7280' viewBox='0 0 16 16'><path d='M8 11L3 6h10l-5 5z'/></svg>");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px;
  padding-right: 2.5rem;
}

/* Grid Layouts */
.datatab-summary-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  margin-bottom: 1.5rem;
}

.datatab-filter-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.datatab-exchange-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
}

.datatab-currency-breakdown {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.datatab-statistics-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Utility Classes */
.datatab-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.datatab-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) var(--bg-secondary);
}

.datatab-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.datatab-scrollbar::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

.datatab-scrollbar::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

.datatab-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Pagination */
.datatab-pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem 0;
  border-top: 1px solid var(--border-color);
}

.datatab-pagination-info {
  font-size: 14px;
  color: var(--text-secondary);
}

.datatab-pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

@media (max-width: 1023px) {
  .datatab-pagination {
    flex-direction: column;
    text-align: center;
  }
  
  .datatab-pagination-numbers {
    display: none;
  }
}

/* Loading States */
.datatab-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
}

.datatab-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--border-color);
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Compact mode for smaller screens */
@media (max-width: 1365px) {
  .datatab-card-header {
    padding: 1rem;
  }
  
  .datatab-card-body {
    padding: 1rem;
  }
  
  .datatab-btn {
    padding: 0.5rem 0.75rem;
    font-size: 14px;
  }
  
  .datatab-input {
    padding: 0.5rem;
    font-size: 13px;
  }
}

@media (max-width: 1023px) {
  .datatab-btn {
    padding: 0.5rem;
  }
  
  .datatab-btn-text-hide {
    display: none;
  }
}

/* Focus and Accessibility */
.datatab-focus:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .datatab-card {
    border: 2px solid var(--text-primary);
  }
  
  .datatab-btn {
    border: 2px solid currentColor;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .datatab-card,
  .datatab-btn,
  .datatab-input,
  .datatab-spinner {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .datatab-btn,
  .datatab-pagination {
    display: none !important;
  }
  
  .datatab-table {
    font-size: 12px;
  }
  
  .datatab-card {
    box-shadow: none;
    border: 1px solid #000;
  }
}