/* App specific styles */
.app-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* Header styles */
.app-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-avatar {
  background: #1890ff;
  font-size: 18px;
  font-weight: bold;
}

.stats-badges {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Stat cards */
.stat-card {
  border-radius: 12px;
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-card-blue {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.stat-card-green {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.stat-card-red {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
}

.stat-card-purple {
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
}

/* Quick actions */
.quick-actions {
  padding: 20px;
}

.quick-actions .ant-space-item {
  margin-bottom: 8px;
}

/* Account and filter cards */
.account-card,
.filter-card {
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.account-card:hover,
.filter-card:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.account-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.account-details {
  flex: 1;
}

.account-email {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.account-date {
  font-size: 11px;
  color: #8c8c8c;
}

/* Filter styles */
.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.filter-name {
  font-size: 14px;
  font-weight: 600;
}

.filter-details {
  font-size: 11px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* Table styles */
.email-table {
  border-radius: 12px;
}

.email-table .ant-table-thead > tr > th {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
  font-size: 12px;
}

.email-table .ant-table-tbody > tr:hover > td {
  background: #fafafa;
}

/* Empty states */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-state-icon {
  font-size: 64px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-state-title {
  color: #8c8c8c;
  margin-bottom: 8px;
}

.empty-state-description {
  color: #bfbfbf;
  font-size: 14px;
}

/* Modal styles */
.modal-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-icon {
  font-size: 16px;
}

/* Loading states */
.loading-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .stats-badges {
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 0 16px;
  }
  
  .logo-section {
    gap: 12px;
  }
  
  .stats-badges {
    display: none;
  }
  
  .quick-actions {
    padding: 16px;
  }
  
  .quick-actions .ant-space {
    flex-wrap: wrap;
  }
  
  .account-card,
  .filter-card {
    margin-bottom: 12px;
  }
}

@media (max-width: 576px) {
  .stat-card .ant-statistic-content-value {
    font-size: 24px !important;
  }
  
  .stat-card .ant-statistic-title {
    font-size: 12px !important;
  }
  
  .empty-state {
    padding: 40px 16px;
  }
  
  .empty-state-icon {
    font-size: 48px;
  }
}